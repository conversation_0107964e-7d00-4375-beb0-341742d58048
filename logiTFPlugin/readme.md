## PreCondition
JDK17
- install refer: https://www.oracle.com/java/technologies/downloads/#java17

Maven
- install refer: https://maven.apache.org/install.html

## Build Tool & Config
#### Copy config  & Copy to logi report server lib folder
pull code from https://bitbucket.org/transfinderorg/logi/src/Develop/logiTFPlugin/

#### Build logiTFPlugin.JAR & Copy to Logi report server lib folder
```
mvn clean package

copy .\target\logiTFPlugin.jar .\{logi report server path}\lib\logiTFPlugin.jar
```

## Run Tool
Restart LogiReport service.
