package jet.formula.javaformula;

import jet.connect.DbChar;
import jet.formula.fImage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

public class RecordPicture {
    private static final Logger logger = LoggerFactory.getLogger(RecordPicture.class);

    public static fImage getRecordPicture(DbChar id, DbChar routeFinderAPI) {
        fImage image = new fImage();
        try {
            if (id.value.isEmpty() || routeFinderAPI.value.isEmpty()) {
                return image;
            }
            BufferedImage bufferedImage = callRouteFinderAPI(routeFinderAPI.value + "recordpicturefiles?id=" + id.value);
            if (bufferedImage == null) {
                return image;
            }
            image.set(bufferedImageToByteArray(bufferedImage));
        } catch (IOException e) {
            logger.error("Failed to get record picture: {}", e.getMessage(), e);
            image.set(new byte[0]);
        }
        return image;
    }

    public static fImage getUDFRecordPicture(DbChar udfImageS3URL) {
        fImage image = new fImage();
        try {
            if (udfImageS3URL.value.isEmpty()) {
                return image;
            }
            BufferedImage bufferedImage = callRouteFinderAPI(udfImageS3URL.value);
            if (bufferedImage == null) {
                return image;
            }

            image.set(bufferedImageToByteArray(bufferedImage));

        } catch (IOException e) {
            LogUtil.log("Failed to get record picture: {}", e.getMessage());
            image.set(new byte[0]);
        }

        LogUtil.log("End: getUDFRecordPicture");
        return image;
    }

    public static BufferedImage callRouteFinderAPI(String routeFinderAPI) {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(routeFinderAPI);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setInstanceFollowRedirects(true);
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                return ImageIO.read(new BufferedInputStream(connection.getInputStream()));
            }

        } catch (IOException e) {
            LogUtil.log("Failed to call RouteFinder plus API: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return null;
    }

    private static byte[] bufferedImageToByteArray(BufferedImage image) throws IOException {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        ImageIO.write(image, "png", stream);
        return stream.toByteArray();
    }
}
