package jet.formula.javaformula;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import jet.connect.DbChar;
import jet.formula.fImage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class QRCodeImage {

    private static final Logger logger = LoggerFactory.getLogger(QRCodeImage.class);

    public static fImage generateImage(DbChar inputText) {
        long startTime = System.currentTimeMillis();

        fImage image = new fImage();
        if(inputText == null || inputText.bNull || inputText.value.isEmpty()){
            logger.debug("QR code content is empty");
            image.set(new byte[0]);
            return image;
        }

        logger.debug("Entering generateImage method with text: {}", inputText.value);

        final int width = 100;
        final int height = 100;
        // Set up QR code generation parameters
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);

        try {
            logger.info("Generating QR code for text: '{}' with dimensions: {}x{}", inputText.value, width, height);

            // Create QR code writer
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            logger.debug("QR code writer created successfully");

            // Generate QR code matrix
            BitMatrix bitMatrix = qrCodeWriter.encode(inputText.value, BarcodeFormat.QR_CODE, width, height, hints);
            logger.debug("QR code matrix generated successfully");

            // Convert to BufferedImage
            BufferedImage qrImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            fillImage(width, height, qrImage, bitMatrix);
            logger.debug("BufferedImage created and filled");

            // Convert to byte array and set in fImage
            image.set(bufferedImageToByteArray(qrImage));
            logger.info("QR code image generated successfully, size: {} bytes", image.value != null ? image.value.length : 0);

            System.out.println("QR code image generated successfully, size: " + (image.value != null ? image.value.length : 0) + " bytes");
            System.out.println(Base64.getEncoder().encodeToString(image.value));

            long duration = System.currentTimeMillis() - startTime;
            logger.info("QR code generation completed in {} ms", duration);
        } catch (WriterException | IOException e) {
            logger.error("Failed to generate QR code: {}", e.getMessage(), e);
            // Handle error - return empty image
            image.set(new byte[0]);
        }

        logger.debug("Exiting generateImage method");
        return image;
    }

    private static void fillImage(int width, int height, BufferedImage qrImage, BitMatrix bitMatrix) {
        // Fill the image
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                qrImage.setRGB(x, y, bitMatrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
            }
        }
    }

    private static byte[] bufferedImageToByteArray(BufferedImage image) throws IOException {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        ImageIO.write(image, "png", stream);
        return stream.toByteArray();
    }
}
