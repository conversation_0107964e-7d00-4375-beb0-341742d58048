package jet.formula.javaformula;

import jet.connect.DbChar;
import jet.formula.fImage;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

public class Application {

    public static void main(String[] args) {
        fImage image = new fImage();
        image.setValue("iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAFJ0lEQVR4Xu2QQY4cMQwD9/+fTg4NCALL5lDu2SAH15Eq0o3++XOJ+dHgsuf+rAH3Zw24P2vA/VkD7s8acH/WgPuzBtyfNeD+rAH3Zw2IftbPO3TODqraME4bOEHnNkSebg/ROTuoasM4beAEndsQebo9ROfsoKoN47SBE3RuQ+RNRx+SVjmJbDirT1uRNx19SFrlJLLhrD5tRd509CFplZPIhrP6tBV5HK2E0GFC6FRC6JiE0KnEE3kcrYTQYULoVELomITQqcQTeRythNBhQuhUQuiYhNCpxBN5HK2E0GEyOtHRw+pUCaFTiSfyOFoJocNkdKKjh9WpEkKnEk/kcbQSQofJ6ERHD6tTJYROJZ7I42glhM5ZcnZiQuhU4ok8jlZC6JwlZycmhE4lnsjjaCWEzllydmJC6FTiibzp6ANblZDW28pqNBKHTFuRNx19YKsS0npbWY1G4pBpK/Kmow9sVUJabyur0UgcMm1FXo2ewZ3fS86oHU/k6fYQ7vxeckbteCJPt4dw5/eSM2rHk3pfQb8xQ1dWP+vf8E/fa39ggK7cn2XQlf/8Z/HjKkloS1rXw4q2pCQOadszoiafaU9/pi1pXQ8r2pKSOKRtz4iafKY9/Zm2pHU9rGhLSuKQtj1j1tRnV5/bdKX1VNbDamd0YvKe2VZ9AT+FCWk9lfWw2hmdmLxntlVfwE9hQlpPZT2sdkYnJu+ZbdUXnMEdJjzRYZKcDNXypN6DPjKEO0x4osMkORmq5Um9B31kCHeY8ESHSXIyVMsTebq9+qamn2B2eGJiTpXwNCVq6murT2n6CWaHJybmVAlPU6Kmvrb6lKafYHZ4YmJOlfA0JWqePcNWJaT1tmhnhXayz2i6I/Kmow9sVUJab4t2Vmgn+4ymOyJvOvrAViWk9bZoZ4V2ss9ouiPydLuhqkXLq7oaDVVXaMe2EqcTee1pRVWLlld1NRqqrtCObSVOJ/La04qqFi2v6mo0VF2hHdtKnE7kmdHkRIcJab0BSZ1OJZ7IM6PJiQ4T0noDkjqdSjyRZ0aTEx0mpPUGJHU6lXgij6OV8DSC9TapJzp6sKeinETuRB5H20N6GsF6m9QTHT3YU1FOIncij6PtIT2NYL1N6omOHuypKCeRO5HH0fbQK9ojuszkJe0RXdbDhsjjaCUvaY/oMpOXtEd0WQ8bIo+jlbykPaLLTF7SHtFlPWxIva/QvlxR1ZK0vuV0Uu8r1McRVS1J61tOJ/W+Qn0cUdWStL7ldCKvRs/QuRXasS06JkmolifydHuIzq3Qjm3RMUlCtTyRp9tDdG6FdmyLjkkSquWJvOnoA1uVGNrA55aqDVUbqsZEzbNn2KrE0AY+t1RtqNpQNSZqnj3DViWGNvC5pWpD1YaqMVGTz7SnFTpJwpNBOxa22tKMqMlnKiF0koQng3YsbLWlGVGTz1RC6CQJTwbtWNhqSzOiJp+phNAxCSknQcunT6ixIfI42h5S6JiElJOg5dMn1NgQeRxtDyl0TELKSdDy6RNqbIg8jraHFDomMZQ8ahm4w8QTeRythNAxiaHkUcvAHSaeyONoJYSOSQwlj1oG7jDxRN509CFp0anEnJicnSoJiQpn60mLTiXmxOTsVElIVDhbT1p0KjEnJmenSkKiQnvxBO68TAxGrhNRdUPk6fYQ7rxMDEauE1F1Q+Tp9hDuvEwMRq4TUXVD6l3+3J814v6sAfdnDbg/a8D9WQPuzxpwf9aA+7MG3J814P6sAfdnDbg/a8Bfnw/rleRYphEAAAAASUVORK5CYII=\n");
        try {
            
            image = QRCodeImage.generateImage(new DbChar("fhdksalhfjdksalh"));
            BufferedImage bi = ImageIO.read(image.getStream());
            ImageIO.write(bi, "png", Files.newOutputStream(Paths.get("C:\\ppppp\\test.png")));
//            InputStream bufferedStream = new BufferedInputStream(image.getStream());
//            Files.write(Paths.get("C:\\ppppp\\test.png"), bufferedStream.readAllBytes());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}
