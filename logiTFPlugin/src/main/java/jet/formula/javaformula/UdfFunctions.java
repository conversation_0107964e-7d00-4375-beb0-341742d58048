package jet.formula.javaformula;
import jet.connect.*;
import java.sql.*;
import jet.formula.*;

public class UdfFunctions
{
	private static final String DB_URL = "**********************************************************************************************************************************";
	private static final String SELECT_VALUE = "select Value from ";
	private static final String WHERE_CLAUSE = " where RecordID= ? and RecordDataType= ? and UDFID= ? and DBID= ?";
	private static final String UDF_TEXT_SQL = SELECT_VALUE + "UDFText" + WHERE_CLAUSE;
	private static final String UDF_BOOLEAN_SQL = SELECT_VALUE + "UDFBoolean" + WHERE_CLAUSE;
	private static final String UDF_CURRENCY_SQL = SELECT_VALUE + "UDFCurrency" + WHERE_CLAUSE;
	private static final String UDF_DATE_SQL = SELECT_VALUE + "UDFDate" + WHERE_CLAUSE;
	private static final String UDF_DATETIME_SQL = SELECT_VALUE + "UDFDatetime" + WHERE_CLAUSE;
	private static final String UDF_EMAIL_SQL = SELECT_VALUE + "UDFEmail" + WHERE_CLAUSE;
	private static final String UDF_HYPERLINK_SQL = SELECT_VALUE + "UDFHyperlink" + WHERE_CLAUSE;
	private static final String UDF_MEMO_SQL = SELECT_VALUE + "UDFMemo" + WHERE_CLAUSE;
	private static final String UDF_NUMERIC_SQL = SELECT_VALUE + "UDFNumeric" + WHERE_CLAUSE;
	private static final String UDF_PHONE_NUMBER_SQL = SELECT_VALUE + "UDFPhoneNumber" + WHERE_CLAUSE;
	private static final String UDF_TIME_SQL = SELECT_VALUE + "UDFTime" + WHERE_CLAUSE;

	public DbChar ReadText(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		return ReadTextValue(recordId, dataType, udfId, dataSourceId, UDF_TEXT_SQL);
	}

	public DbChar ReadEmail(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		return ReadTextValue(recordId, dataType, udfId, dataSourceId, UDF_EMAIL_SQL);
	}

	public DbChar ReadHyperLink(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		return ReadTextValue(recordId, dataType, udfId, dataSourceId, UDF_HYPERLINK_SQL);
	}

	public DbChar ReadPhoneNumber(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		return ReadTextValue(recordId, dataType, udfId, dataSourceId, UDF_PHONE_NUMBER_SQL);
	}

	public DbChar ReadMemo(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		return ReadTextValue(recordId, dataType, udfId, dataSourceId, UDF_MEMO_SQL);
	}

	public DbBit ReadBoolean(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		DbBit result = new DbBit();
		result.bNull = true;

		try (Connection connection = DriverManager.getConnection(DB_URL); PreparedStatement statement = connection.prepareStatement(UDF_BOOLEAN_SQL))
		{
			SetParameters(recordId, dataType, udfId, dataSourceId, statement);

			try(ResultSet rs = statement.executeQuery())
			{
				while (rs.next())
				{
					result.bNull = false;
					result.value = rs.getBoolean(1);
				}
			}

		} catch (SQLException ignored) {
		}

		return result;
	}

	public fCurrency ReadCurrency(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		fCurrency result = new fCurrency();
		result.bNull = true;

		try (Connection connection = DriverManager.getConnection(DB_URL); PreparedStatement statement = connection.prepareStatement(UDF_CURRENCY_SQL))
		{
			SetParameters(recordId, dataType, udfId, dataSourceId, statement);

			try(ResultSet rs = statement.executeQuery())
			{
				while (rs.next())
				{
					result.value = rs.getBigDecimal(1);
					result.bNull = result.value == null;
				}
			}

		} catch (SQLException ignored) {
		}

		return result;
	}

	public DbDate ReadDate(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		DbDate result = new DbDate();
		result.bNull = true;

		try (Connection connection = DriverManager.getConnection(DB_URL); PreparedStatement statement = connection.prepareStatement(UDF_DATE_SQL))
		{
			SetParameters(recordId, dataType, udfId, dataSourceId, statement);

			try(ResultSet rs = statement.executeQuery())
			{
				while (rs.next())
				{
					Date value = rs.getDate(1);
					if(value != null)
					{
						result.bNull = false;
						result.value = rs.getDate(1).getTime();
					}
				}
			}

		} catch (SQLException ignored) {
		}

		return result;
	}

	public DbTimestamp ReadDateTime(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		DbTimestamp result = new DbTimestamp();
		result.bNull = true;

		try (Connection connection = DriverManager.getConnection(DB_URL); PreparedStatement statement = connection.prepareStatement(UDF_DATETIME_SQL))
		{
			SetParameters(recordId, dataType, udfId, dataSourceId, statement);

			try(ResultSet rs = statement.executeQuery())
			{
				while (rs.next())
				{
					Timestamp value = rs.getTimestamp(1);
					if(value != null)
					{
						result.bNull = false;
						result.value = value.getTime();
					}

				}
			}

		} catch (SQLException ignored) {
		}

		return result;
	}

	public DbTime ReadTime(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		DbTime result = new DbTime();
		result.bNull = true;

		try (Connection connection = DriverManager.getConnection(DB_URL); PreparedStatement statement = connection.prepareStatement(UDF_TIME_SQL))
		{
			SetParameters(recordId, dataType, udfId, dataSourceId, statement);

			try(ResultSet rs = statement.executeQuery())
			{
				while (rs.next())
				{
					Time value = rs.getTime(1);
					if(value != null)
					{
						result.bNull = false;
						result.value = value.getTime();
					}
				}
			}

		} catch (SQLException ignored) {
		}

		return result;
	}

	public DbDouble ReadNumeric(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId) {
		DbDouble result = new DbDouble();
		result.bNull = true;

		try (Connection connection = DriverManager.getConnection(DB_URL); PreparedStatement statement = connection.prepareStatement(UDF_NUMERIC_SQL))
		{
			SetParameters(recordId, dataType, udfId, dataSourceId, statement);

			try(ResultSet rs = statement.executeQuery())
			{
				while (rs.next())
				{
					Object value = rs.getObject(1);
					if(value instanceof Number numberValue)
					{
						result.bNull = false;
						result.value = numberValue.doubleValue();
					}

				}
			}

		} catch (SQLException ignored) {
		}

		return result;
	}

	private static DbChar ReadTextValue(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId, String sql) {
		DbChar result = new DbChar();
		result.bNull = true;

		try (Connection connection = DriverManager.getConnection(DB_URL); PreparedStatement statement = connection.prepareStatement(sql))
		{
			SetParameters(recordId, dataType, udfId, dataSourceId, statement);

			try(ResultSet rs = statement.executeQuery())
			{
				while (rs.next())
				{
					result.bNull = false;
					result.value = rs.getString(1);
				}
			}

		} catch (SQLException ignored) {
		}

		return result;
	}

	private static void SetParameters(DbBigInt recordId, DbBigInt dataType, DbBigInt udfId, DbBigInt dataSourceId, PreparedStatement statement) throws SQLException {
		statement.setLong(1, recordId.value);
		statement.setLong(2, dataType.value);
		statement.setLong(3, udfId.value);
		statement.setLong(4, dataSourceId.value);
	}
}