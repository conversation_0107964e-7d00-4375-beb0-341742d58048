package jet.formula.javaformula;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

public class LogUtil {
    public static void log(String[] messages) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timestamp = LocalDateTime.now().format(formatter);

        try (BufferedWriter out = new BufferedWriter(new FileWriter("LogiTFPlugin.log.txt", true))) {
            out.write("[" + timestamp + "] " + Arrays.toString(messages));
            out.newLine();
            System.out.println("[" + timestamp + "] LogiPlug in Log Wrote！");

        } catch (IOException e) {
            System.err.println("[" + timestamp + "] Exception： " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void log(String message) {
        log(new String[]{message});
    }

    public static void log(String message1, String message2) {
        log(new String[]{message1, message2});
    }
}
